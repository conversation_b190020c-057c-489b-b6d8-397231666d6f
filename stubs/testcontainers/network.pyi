from typing import Self

class Network:
    def __init__(
        self,
        docker_client_kw: dict | None = ...,
        docker_network_kw: dict | None = ...,
    ) -> None: ...
    def connect(self, container_id: str, network_aliases: list | None = ...): ...
    def remove(self) -> None: ...
    def create(self) -> Network: ...
    def __enter__(self) -> Self: ...
    def __exit__(self, exc_type, exc_val, exc_tb) -> None: ...
