from os import <PERSON><PERSON>ike
from socket import socket
from typing import TYPE_CHECKING, Self

from docker.models.containers import Container
from testcontainers.core.docker_client import DockerClient
from testcontainers.core.network import Network
from testcontainers.core.waiting_utils import wait_container_is_ready

if TYPE_CHECKING: ...
logger = ...

class DockerContainer:
    def __init__(
        self, image: str, docker_client_kw: dict | None = ..., **kwargs
    ) -> None: ...
    def with_env(self, key: str, value: str) -> Self: ...
    def with_env_file(self, env_file: str | PathLike) -> Self: ...
    def with_bind_ports(self, container: int, host: int | None = ...) -> Self: ...
    def with_exposed_ports(self, *ports: int) -> Self: ...
    def with_network(self, network: Network) -> Self: ...
    def with_network_aliases(self, *aliases) -> Self: ...
    def with_kwargs(self, **kwargs) -> Self: ...
    def maybe_emulate_amd64(self) -> Self: ...
    def start(self) -> Self: ...
    def stop(self, force=..., delete_volume=...) -> None: ...
    def __enter__(self) -> Self: ...
    def __exit__(self, exc_type, exc_val, exc_tb) -> None: ...
    def get_container_host_ip(self) -> str: ...
    @wait_container_is_ready()
    def get_exposed_port(self, port: int) -> int: ...
    def with_command(self, command: str) -> Self: ...
    def with_name(self, name: str) -> Self: ...
    def with_volume_mapping(
        self, host: str, container: str, mode: str = ...
    ) -> Self: ...
    def get_wrapped_container(self) -> Container: ...
    def get_docker_client(self) -> DockerClient: ...
    def get_logs(self) -> tuple[bytes, bytes]: ...
    def exec(self, command: str | list[str]) -> tuple[int, bytes]: ...

class Reaper:
    _instance: Reaper | None = ...
    _container: DockerContainer | None = ...
    _socket: socket | None = ...

    @classmethod
    def get_instance(cls) -> Reaper: ...
    @classmethod
    def delete_instance(cls) -> None: ...
