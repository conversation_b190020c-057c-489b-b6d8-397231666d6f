from typing import Any

DockerAuthInfo = ...
_AUTH_WARNINGS = ...

def process_docker_auth_config_encoded(
    auth_config_dict: dict[str, dict[str, dict[str, Any]]],
) -> list[DockerAuthInfo]: ...
def process_docker_auth_config_cred_helpers(
    auth_config_dict: dict[str, Any],
) -> None: ...
def process_docker_auth_config_store(auth_config_dict: dict[str, Any]) -> None: ...
def parse_docker_auth_config(auth_config: str) -> list[DockerAuthInfo] | None: ...
