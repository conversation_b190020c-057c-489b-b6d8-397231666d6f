from dataclasses import dataclass
from enum import Enum

class ConnectionMode(Enum):
    bridge_ip = ...
    gateway_ip = ...
    docker_host = ...

    @property
    def use_mapped_port(self) -> bool: ...

MAX_TRIES = ...
SLEEP_TIME = ...
TIMEOUT = ...
RYUK_IMAGE: str = ...
RYUK_PRIVILEGED: bool = ...
RYUK_DISABLED: bool = ...
RYUK_DOCKER_SOCKET: str = ...
RYUK_RECONNECTION_TIMEOUT: str = ...
TC_HOST_OVERRIDE: str | None = ...
TC_FILE = ...
TC_GLOBAL = ...

def get_user_overwritten_connection_mode() -> ConnectionMode | None: ...
def read_tc_properties() -> dict[str, str]: ...

_WARNINGS = ...

@dataclass
class TestcontainersConfiguration:
    max_tries: int = ...
    sleep_time: int = ...
    ryuk_image: str = ...
    ryuk_privileged: bool = ...
    ryuk_disabled: bool = ...
    ryuk_docker_socket: str = ...
    ryuk_reconnection_timeout: str = ...
    tc_properties: dict[str, str] = ...
    _docker_auth_config: str | None = ...
    tc_host_override: str | None = ...
    connection_mode_override: ConnectionMode | None = ...

    @property
    def docker_auth_config(self) -> str | None: ...
    @docker_auth_config.setter
    def docker_auth_config(self, value: str) -> None: ...
    def tc_properties_get_tc_host(self) -> str | None: ...
    @property
    def timeout(self) -> int: ...

testcontainers_config = ...
__all__ = [
    "MAX_TRIES",
    "RYUK_DISABLED",
    "RYUK_DOCKER_SOCKET",
    "RYUK_IMAGE",
    "RYUK_PRIVILEGED",
    "RYUK_RECONNECTION_TIMEOUT",
    "SLEEP_TIME",
    "TIMEOUT",
    "testcontainers_config",
]
