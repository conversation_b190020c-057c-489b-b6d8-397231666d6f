"""Docker image management type stubs."""

from os import PathLike
from typing import Self
from collections.abc import Iterator

class DockerImage:
    """Docker image builder and manager."""
    
    def __init__(
        self,
        path: str | PathLike[str],
        tag: str,
        dockerfile: str = "Dockerfile",
        **kwargs: object,
    ) -> None:
        """Initialize Docker image builder.
        
        Args:
            path: Path to build context
            tag: Image tag
            dockerfile: Dockerfile name
            **kwargs: Additional build arguments
        """
        ...
    
    def __enter__(self) -> Self:
        """Enter context manager and build image."""
        ...
    
    def __exit__(self, exc_type: object, exc_val: object, exc_tb: object) -> None:
        """Exit context manager and cleanup."""
        ...
    
    def __str__(self) -> str:
        """Return image tag as string."""
        ...
    
    def build(self) -> Self:
        """Build the Docker image."""
        ...
    
    def remove(self) -> None:
        """Remove the built image."""
        ...
    
    @property
    def tag(self) -> str:
        """Get the image tag."""
        ...
    
    @property
    def short_id(self) -> str:
        """Get the short image ID."""
        ...
    
    def get_logs(self) -> Iterator[dict[str, object]]:
        """Get build logs."""
        ...
