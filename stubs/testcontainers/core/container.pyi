"""Docker container management type stubs."""

from os import <PERSON><PERSON><PERSON>
from socket import socket
from typing import Any, Self
from collections.abc import Sequence

from docker.models.containers import Container
from testcontainers.core.docker_client import DockerClient
from testcontainers.core.network import Network

logger: Any

class DockerContainer:
    """Docker container wrapper for testcontainers."""
    
    def __init__(
        self, 
        image: str, 
        docker_client_kw: dict[str, Any] | None = None, 
        **kwargs: Any
    ) -> None:
        """Initialize Docker container.
        
        Args:
            image: Docker image name
            docker_client_kw: Docker client configuration
            **kwargs: Additional container arguments
        """
        ...
    
    def with_env(self, key: str, value: str) -> Self:
        """Set environment variable."""
        ...
    
    def with_env_file(self, env_file: str | PathLike[str]) -> Self:
        """Load environment from file."""
        ...
    
    def with_bind_ports(self, container: int, host: int | None = None) -> Self:
        """Bind container port to host port."""
        ...
    
    def with_exposed_ports(self, *ports: int) -> Self:
        """Expose container ports."""
        ...
    
    def with_network(self, network: Network) -> Self:
        """Connect to network."""
        ...
    
    def with_network_aliases(self, *aliases: str) -> Self:
        """Set network aliases."""
        ...
    
    def with_kwargs(self, **kwargs: Any) -> Self:
        """Set additional container arguments."""
        ...
    
    def maybe_emulate_amd64(self) -> Self:
        """Enable AMD64 emulation if needed."""
        ...
    
    def start(self) -> Self:
        """Start the container."""
        ...
    
    def stop(self, force: bool = True, delete_volume: bool = True) -> None:
        """Stop the container."""
        ...
    
    def __enter__(self) -> Self:
        """Enter context manager."""
        ...
    
    def __exit__(
        self, 
        exc_type: type[BaseException] | None, 
        exc_val: BaseException | None, 
        exc_tb: Any
    ) -> None:
        """Exit context manager."""
        ...
    
    def get_container_host_ip(self) -> str:
        """Get container host IP address."""
        ...
    
    def get_exposed_port(self, port: int) -> int:
        """Get exposed host port for container port."""
        ...
    
    def with_command(self, command: str | Sequence[str]) -> Self:
        """Set container command."""
        ...
    
    def with_name(self, name: str) -> Self:
        """Set container name."""
        ...
    
    def with_volume_mapping(
        self, 
        host: str, 
        container: str, 
        mode: str = "rw"
    ) -> Self:
        """Mount volume from host to container."""
        ...
    
    def get_wrapped_container(self) -> Container:
        """Get underlying Docker container object."""
        ...
    
    def get_docker_client(self) -> DockerClient:
        """Get Docker client instance."""
        ...
    
    def get_logs(self) -> tuple[bytes, bytes]:
        """Get container logs (stdout, stderr)."""
        ...
    
    def exec(self, command: str | Sequence[str]) -> tuple[int, bytes]:
        """Execute command in container."""
        ...


class Reaper:
    """Container cleanup manager."""
    
    _instance: Reaper | None
    _container: DockerContainer | None
    _socket: socket | None

    @classmethod
    def get_instance(cls) -> Reaper:
        """Get singleton Reaper instance."""
        ...
    
    @classmethod
    def delete_instance(cls) -> None:
        """Delete Reaper instance."""
        ...
