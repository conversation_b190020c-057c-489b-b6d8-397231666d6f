"""Docker client wrapper type stubs."""

from typing import Any, Callable, TypeVar
from collections.abc import Iterable, Sequence

from docker.models.containers import Container
from docker.models.images import Image

LOGGER: Any

F = TypeVar("F", bound=Callable[..., Any])

def _wrapped_container_collection(func: F) -> F:
    """Decorator for container collection methods."""
    ...

def _wrapped_image_collection(func: F) -> F:
    """Decorator for image collection methods."""
    ...

class DockerClient:
    """Docker client wrapper for testcontainers."""
    
    def __init__(self, **kwargs: Any) -> None:
        """Initialize Docker client.
        
        Args:
            **kwargs: Docker client configuration
        """
        ...
    
    @_wrapped_container_collection
    def run(
        self,
        image: str,
        command: str | Sequence[str] | None = None,
        environment: dict[str, str] | None = None,
        ports: dict[str, Any] | None = None,
        labels: dict[str, str] | None = None,
        detach: bool = True,
        stdout: bool = True,
        stderr: bool = True,
        remove: bool = False,
        **kwargs: Any,
    ) -> Container:
        """Run a container.
        
        Args:
            image: Docker image name
            command: Command to run
            environment: Environment variables
            ports: Port mappings
            labels: Container labels
            detach: Run in detached mode
            stdout: Capture stdout
            stderr: Capture stderr
            remove: Remove container after exit
            **kwargs: Additional arguments
            
        Returns:
            Docker container object
        """
        ...
    
    @_wrapped_image_collection
    def build(
        self, 
        path: str, 
        tag: str, 
        rm: bool = True, 
        **kwargs: Any
    ) -> tuple[Image, Iterable[dict[str, Any]]]:
        """Build Docker image.
        
        Args:
            path: Build context path
            tag: Image tag
            rm: Remove intermediate containers
            **kwargs: Additional build arguments
            
        Returns:
            Tuple of (image, build_logs)
        """
        ...
    
    def find_host_network(self) -> str | None:
        """Find host network name."""
        ...
    
    def port(self, container_id: str, port: int) -> int:
        """Get host port for container port."""
        ...
    
    def get_container(self, container_id: str) -> Container:
        """Get container by ID."""
        ...
    
    def bridge_ip(self, container_id: str) -> str:
        """Get bridge IP for container."""
        ...
    
    def network_name(self, container_id: str) -> str:
        """Get network name for container."""
        ...
