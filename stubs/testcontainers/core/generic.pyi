"""Generic container implementations type stubs."""

from typing import Any, Self
from testcontainers.core.container import DockerContainer

class ServerContainer(DockerContainer):
    """Generic server container with HTTP client support."""
    
    def __init__(
        self,
        image: str,
        port: int,
        **kwargs: Any,
    ) -> None:
        """Initialize server container.
        
        Args:
            image: Docker image name
            port: Server port to expose
            **kwargs: Additional container arguments
        """
        ...
    
    def get_api_url(self) -> str:
        """Get the API URL for the server."""
        ...
    
    def get_client(self) -> Any:
        """Get HTTP client for the server."""
        ...
    
    def _create_connection_url(self) -> str:
        """Create connection URL for the server."""
        ...
