"""Container waiting utilities type stubs."""

from typing import Any, Callable, TypeVar
from collections.abc import Sequence

from testcontainers.core.container import DockerContainer

logger: Any
TRANSIENT_EXCEPTIONS: tuple[type[Exception], ...]
_NOT_EXITED_STATUSES: Sequence[str]

F = TypeVar("F", bound=Callable[..., Any])

def wait_container_is_ready(
    *transient_exceptions: type[Exception]
) -> Callable[[F], F]:
    """Decorator to retry function until container is ready.
    
    Args:
        *transient_exceptions: Exception types to retry on
        
    Returns:
        Decorator function
    """
    ...

@wait_container_is_ready()
def wait_for(condition: Callable[..., bool]) -> bool:
    """Wait for a condition to be true.
    
    Args:
        condition: Function that returns True when ready
        
    Returns:
        True when condition is met
    """
    ...

def wait_for_logs(
    container: DockerContainer,
    predicate: Callable[[str], bool] | str,
    timeout: float = 120,
    interval: float = 1,
    predicate_streams_and: bool = True,
    raise_on_exit: bool = True,
) -> float:
    """Wait for specific logs to appear in container.
    
    Args:
        container: Container to monitor
        predicate: String to search for or callable to test log lines
        timeout: Maximum time to wait in seconds
        interval: Check interval in seconds
        predicate_streams_and: Whether to check both stdout and stderr
        raise_on_exit: Whether to raise if container exits
        
    Returns:
        Time elapsed until logs appeared
        
    Raises:
        TimeoutError: If timeout is reached
        RuntimeError: If container exits and raise_on_exit is True
    """
    ...
