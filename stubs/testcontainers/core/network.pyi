from typing import Any, Self

class Network:
    def __init__(
        self,
        docker_client_kw: dict[str, Any] | None = None,
        docker_network_kw: dict[str, Any] | None = None,
    ) -> None: ...
    def connect(
        self, 
        container_id: str, 
        network_aliases: list[str] | None = None
    ) -> None: ...
    def remove(self) -> None: ...
    def create(self) -> Network: ...
    def __enter__(self) -> Self: ...
    def __exit__(
        self, 
        exc_type: type[BaseException] | None, 
        exc_val: BaseException | None, 
        exc_tb: Any
    ) -> None: ...
