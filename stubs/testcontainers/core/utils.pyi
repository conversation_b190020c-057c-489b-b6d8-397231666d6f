import logging
from pathlib import Path
from typing import Any, Final

LINUX: str
MAC: str
WIN: str

def setup_logger(name: str) -> logging.Logger: ...
def os_name() -> str | None: ...
def is_mac() -> bool: ...
def is_linux() -> bool: ...
def is_windows() -> bool: ...
def is_arm() -> bool: ...
def inside_container() -> bool: ...
def default_gateway_ip() -> str | None: ...
def raise_for_deprecated_parameter(
    kwargs: dict[Any, Any], name: str, replacement: str
) -> dict[Any, Any]: ...

CGROUP_FILE: Final[Path]

def get_running_in_container_id() -> str | None: ...
