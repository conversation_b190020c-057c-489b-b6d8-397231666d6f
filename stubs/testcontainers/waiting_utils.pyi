from collections.abc import Callable
from typing import TYPE_CHECKING

from testcontainers.core.container import DockerContainer

if TYPE_CHECKING: ...
logger = ...
TRANSIENT_EXCEPTIONS = ...

def wait_container_is_ready(*transient_exceptions) -> Callable: ...
@wait_container_is_ready()
def wait_for(condition: Callable[..., bool]) -> bool: ...

_NOT_EXITED_STATUSES = ...

def wait_for_logs(
    container: DockerContainer,
    predicate: Callable | str,
    timeout: float = ...,
    interval: float = ...,
    predicate_streams_and: bool = ...,
    raise_on_exit: bool = ...,
) -> float: ...
