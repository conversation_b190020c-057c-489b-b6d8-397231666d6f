"""Legacy import location for DockerClient - use testcontainers.core.docker_client instead."""

from testcontainers.core.docker_client import DockerClient as DockerClient

__all__ = ["DockerClient"]
    def gateway_ip(self, container_id: str) -> str: ...
    def get_connection_mode(self) -> ConnectionMode: ...
    def host(self) -> str: ...
    def login(self, auth_config: DockerAuthInfo) -> None: ...
    def client_networks_create(self, name: str, param: dict): ...

def get_docker_host() -> str | None: ...
def get_docker_auth_config() -> str | None: ...
