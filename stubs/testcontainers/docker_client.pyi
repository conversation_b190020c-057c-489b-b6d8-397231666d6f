from collections.abc import Iterable

from docker.models.containers import Container
from docker.models.images import Image
from testcontainers.core.auth import DockerAuthInfo
from testcontainers.core.config import ConnectionMode

LOGGER = ...

class DockerClient:
    def __init__(self, **kwargs) -> None: ...
    @_wrapped_container_collection
    def run(
        self,
        image: str,
        command: str | list[str] | None = ...,
        environment: dict | None = ...,
        ports: dict | None = ...,
        labels: dict[str, str] | None = ...,
        detach: bool = ...,
        stdout: bool = ...,
        stderr: bool = ...,
        remove: bool = ...,
        **kwargs,
    ) -> Container: ...
    @_wrapped_image_collection
    def build(
        self, path: str, tag: str, rm: bool = ..., **kwargs
    ) -> tuple[Image, Iterable[dict]]: ...
    def find_host_network(self) -> str | None: ...
    def port(self, container_id: str, port: int) -> int: ...
    def get_container(self, container_id: str) -> Container: ...
    def bridge_ip(self, container_id: str) -> str: ...
    def network_name(self, container_id: str) -> str: ...
    def gateway_ip(self, container_id: str) -> str: ...
    def get_connection_mode(self) -> ConnectionMode: ...
    def host(self) -> str: ...
    def login(self, auth_config: DockerAuthInfo) -> None: ...
    def client_networks_create(self, name: str, param: dict): ...

def get_docker_host() -> str | None: ...
def get_docker_auth_config() -> str | None: ...
