"""Container builder for NAO simulation."""

import subprocess
from pathlib import Path

from pydantic import BaseModel
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


class BuildConfig(BaseModel):
    """Configuration for container build."""

    tag: str = "nao-simulation:latest"
    platform: str | None = None
    no_cache: bool = False
    push: bool = False
    registry: str | None = None
    optimize: bool = True


class ContainerBuilder:
    """Builder for NAO simulation container."""

    def __init__(self, dockerfile_path: Path | None = None) -> None:
        """Initialize builder.

        Args:
            dockerfile_path: Path to Dockerfile (defaults to package directory)
        """
        if dockerfile_path is None:
            # Default to package directory
            package_dir = Path(__file__).parent.parent.parent
            dockerfile_path = package_dir / "Dockerfile"

        self.dockerfile_path: Path | None = dockerfile_path
        self.build_context: Path = dockerfile_path.parent

    def build(self, config: BuildConfig) -> None:
        """Build the container image.

        Args:
            config: Build configuration

        Raises:
            subprocess.CalledProcessError: If the build fails
        """
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Building NAO simulation container...", total=None)

            try:
                self._build_image(config)
                progress.update(task, description="✅ Container built successfully")

                if config.push:
                    progress.update(task, description="Pushing to registry...")
                    ContainerBuilder._push_image(config)
                    progress.update(
                        task, description="✅ Container pushed successfully"
                    )

            except subprocess.CalledProcessError as e:
                progress.update(task, description="❌ Build failed")
                console.print(f"[red]Build failed: {e}[/red]")
                raise

    def _build_image(self, config: BuildConfig) -> None:
        """Build Docker image."""
        cmd = ["docker", "build"]

        # Add build arguments
        if config.optimize:
            cmd.extend(["--build-arg", "BUILDKIT_INLINE_CACHE=1"])

        # Add platform if specified
        if config.platform:
            cmd.extend(["--platform", config.platform])

        # Add no-cache flag
        if config.no_cache:
            cmd.append("--no-cache")

        # Add tag
        cmd.extend(["-t", config.tag])

        # Add registry tag if pushing
        if config.push and config.registry:
            registry_tag = f"{config.registry}/{config.tag}"
            cmd.extend(["-t", registry_tag])

        # Add Dockerfile and context
        cmd.extend(["-f", str(self.dockerfile_path), str(self.build_context)])

        console.print(f"[dim]Running: {' '.join(cmd)}[/dim]")
        subprocess.run(cmd, check=True, capture_output=False)

    @staticmethod
    def _push_image(config: BuildConfig) -> None:
        """Push image to registry."""
        if not config.registry:
            msg = "Registry must be specified for push"
            raise ValueError(msg)

        registry_tag = f"{config.registry}/{config.tag}"
        cmd = ["docker", "push", registry_tag]

        console.print(f"[dim]Running: {' '.join(cmd)}[/dim]")
        subprocess.run(cmd, check=True, capture_output=False)

    @staticmethod
    def cleanup() -> None:
        """Clean up build artifacts."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Cleaning up...", total=None)

            try:
                # Remove dangling images
                subprocess.run(
                    ["docker", "image", "prune", "-f"],
                    check=True,
                    capture_output=True,
                )
                progress.update(task, description="✅ Cleanup completed")

            except subprocess.CalledProcessError:
                progress.update(task, description="⚠️ Cleanup skipped")

    @staticmethod
    def get_image_size(tag: str) -> str:
        """Get the size of a built image.

        Args:
            tag: Image tag

        Returns:
            Human-readable size string
        """
        try:
            result = subprocess.run(
                ["docker", "images", "--format", "{{.Size}}", tag],
                capture_output=True,
                text=True,
                check=True,
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return "Unknown"
