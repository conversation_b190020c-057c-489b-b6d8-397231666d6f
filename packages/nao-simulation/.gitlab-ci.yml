# GitLab CI configuration for NAO simulation container
# This file demonstrates how to build and push the simulation container in CI

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  SIMULATION_TAG: "${CI_REGISTRY_IMAGE}/nao-simulation:${CI_COMMIT_SHORT_SHA}"
  SIMULATION_LATEST_TAG: "${CI_REGISTRY_IMAGE}/nao-simulation:latest"

services:
  - docker:dind

stages:
  - build
  - test  
  - deploy

# Build simulation container
build_simulation:
  stage: build
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Build with both commit SHA and latest tags
    - cd packages/nao-simulation
    - docker build 
        --build-arg BUILDKIT_INLINE_CACHE=1
        --cache-from ${SIMULATION_LATEST_TAG}
        -t ${SIMULATION_TAG}
        -t ${SIMULATION_LATEST_TAG}
        .
    # Push both tags
    - docker push ${SIMULATION_TAG}
    - docker push ${SIMULATION_LATEST_TAG}
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Test simulation container
test_simulation:
  stage: test
  image: python:3.12
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - pip install uv
    - cd packages/nao-simulation
    - uv sync --dev
  script:
    # Test that container can be built and started
    - uv run python -m pytest tests/ -v
  dependencies:
    - build_simulation
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Deploy to production registry (only on main branch)
deploy_simulation:
  stage: deploy
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker pull ${SIMULATION_TAG}
    # Tag as production release
    - docker tag ${SIMULATION_TAG} ${CI_REGISTRY_IMAGE}/nao-simulation:production
    - docker push ${CI_REGISTRY_IMAGE}/nao-simulation:production
  dependencies:
    - test_simulation
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  environment:
    name: production